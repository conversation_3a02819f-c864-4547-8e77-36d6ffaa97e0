"use client"

import React, { useState, useEffect, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  ArrowRight,
  Plus,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye,
  Calendar,
  DollarSign,
  Building,
  ChevronLeft,
} from "lucide-react"
import Image from "next/image"

// Types
interface RentalAdvanceRequest {
  id: string
  rentAmount: number
  monthsToAdvance: number
  currentStatus: string
  statusLabel: string
  proposalAmount?: number
  monthlyRentOffer?: number
  proposedMonths?: number
  realEstate?: {
    name: string
    cnpj: string
  }
  createdAt: string
  updatedAt: string
  canEdit: boolean
  canCancel: boolean
  nextSteps: string[]
}

interface DashboardState {
  requests: RentalAdvanceRequest[]
  loading: boolean
  error: string
  user: any | null
  authToken: string | null
}

export default function DashboardPage() {
  const [state, setState] = useState<DashboardState>({
    requests: [],
    loading: true,
    error: "",
    user: null,
    authToken: null,
  })

  // Load user data from localStorage on component mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem("locpay_app_state_v1")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        if (parsed.user && parsed.authToken) {
          setState(prev => ({
            ...prev,
            user: parsed.user,
            authToken: parsed.authToken,
          }))
        } else {
          // Redirect to login if no auth data
          window.location.href = "/"
        }
      } else {
        // Redirect to login if no saved state
        window.location.href = "/"
      }
    } catch (error) {
      console.error("Error loading user data:", error)
      window.location.href = "/"
    }
  }, [])

  // Fetch rental advance requests
  const fetchRequests = useCallback(async () => {
    if (!state.authToken) return

    setState(prev => ({ ...prev, loading: true, error: "" }))

    try {
      const response = await fetch("/api/v1/rental-advance", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${state.authToken}`,
        },
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setState(prev => ({
          ...prev,
          requests: result.data || [],
          loading: false,
        }))
      } else {
        setState(prev => ({
          ...prev,
          error: result.message || "Erro ao carregar solicitações",
          loading: false,
        }))
      }
    } catch (error) {
      console.error("Error fetching requests:", error)
      setState(prev => ({
        ...prev,
        error: "Erro de conexão. Tente novamente.",
        loading: false,
      }))
    }
  }, [state.authToken])

  // Fetch requests when authToken is available
  useEffect(() => {
    if (state.authToken) {
      fetchRequests()
    }
  }, [state.authToken, fetchRequests])

  // Format currency
  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    const statusMap: Record<string, { icon: React.ReactNode; color: string; bg: string }> = {
      created: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pdf_uploaded: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pdf_extracted: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      data_confirmed: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pending_proposal: { icon: <Clock className="w-4 h-4" />, color: "text-yellow-600", bg: "bg-yellow-50" },
      proposal_sent: { icon: <AlertCircle className="w-4 h-4" />, color: "text-orange-600", bg: "bg-orange-50" },
      docs_uploaded: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      awaiting_review: { icon: <Clock className="w-4 h-4" />, color: "text-purple-600", bg: "bg-purple-50" },
      approved: { icon: <CheckCircle className="w-4 h-4" />, color: "text-green-600", bg: "bg-green-50" },
      rejected: { icon: <XCircle className="w-4 h-4" />, color: "text-red-600", bg: "bg-red-50" },
      cancelled: { icon: <XCircle className="w-4 h-4" />, color: "text-gray-600", bg: "bg-gray-50" },
    }
    return statusMap[status] || statusMap.created
  }

  // Handle new request
  const handleNewRequest = () => {
    window.location.href = "/"
  }

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem("locpay_app_state_v1")
    window.location.href = "/"
  }

  // Style classes (matching page.tsx)
  const mainButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const greenButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"

  if (state.loading && !state.requests.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center p-8 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-sm mx-4 border border-white/20">
          <div className="relative mb-8">
            <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin mx-auto">
              <div className="w-full h-full border-4 border-transparent border-t-blue-600 border-r-blue-700 rounded-full animate-spin"></div>
            </div>
          </div>
          <p className="text-[#0B4375] font-bold text-lg">Carregando suas solicitações...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleLogout}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Sair"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={fetchRequests}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Atualizar"
        >
          <RefreshCw className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4 pb-8">
        {/* Welcome Section */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Olá, {state.user?.name?.split(" ")[0] || "Usuário"}!
          </h1>
          <p className="text-white/90 text-sm">Suas solicitações de antecipação de aluguel</p>
        </div>

        {/* New Request Button */}
        <div className="mb-6">
          <Button onClick={handleNewRequest} className={greenButtonClass}>
            <Plus className="w-4 h-4" />
            Nova Solicitação
            <ArrowRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Error Display */}
        {state.error && (
          <Card className="bg-red-50 border-red-200 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{state.error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Requests List */}
        {state.requests.length === 0 && !state.loading ? (
          <Card className="bg-white shadow-xl rounded-2xl border-0">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Nenhuma solicitação encontrada</h3>
              <p className="text-gray-600 text-sm mb-6">
                Você ainda não possui solicitações de antecipação de aluguel.
              </p>
              <Button onClick={handleNewRequest} className={mainButtonClass}>
                <Plus className="w-4 h-4" />
                Fazer Primeira Solicitação
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {state.requests.map((request) => {
              const statusDisplay = getStatusDisplay(request.currentStatus)
              return (
                <Card key={request.id} className="bg-white shadow-xl rounded-2xl border-0">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <div className={`p-2 rounded-lg ${statusDisplay.bg}`}>
                            <div className={statusDisplay.color}>{statusDisplay.icon}</div>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-800">{request.statusLabel}</h3>
                            <p className="text-xs text-gray-500">ID: {request.id.slice(-8)}</p>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#0B4375] hover:bg-blue-50"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Valor Mensal</p>
                          <p className="font-semibold text-gray-800">{formatCurrency(request.rentAmount)}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Meses</p>
                          <p className="font-semibold text-gray-800">{request.monthsToAdvance}</p>
                        </div>
                      </div>
                    </div>

                    {request.realEstate && (
                      <div className="flex items-center gap-2 mb-4">
                        <Building className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Imobiliária</p>
                          <p className="text-sm text-gray-700">{request.realEstate.name}</p>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          Criado em {formatDate(request.createdAt)}
                        </span>
                      </div>
                      {request.proposalAmount && (
                        <div className="text-right">
                          <p className="text-xs text-gray-500">Proposta</p>
                          <p className="font-semibold text-green-600">{formatCurrency(request.proposalAmount)}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
